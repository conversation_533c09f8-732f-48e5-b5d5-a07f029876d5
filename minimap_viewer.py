#!/usr/bin/env python3
"""
Minimap Viewer Component for FiendishFinder

A PyQt6-based minimap viewer with zoom functionality, floor navigation,
and camera position preservation for Tibia minimap exploration.
"""

import sys
import math
from pathlib import Path
from typing import Dict, Optional, List
import logging

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QGraphicsView, QGraphicsScene, QGraphicsPixmapItem, QPushButton,
    QComboBox, QLabel, QSlider, QFrame, QSizePolicy, QMessageBox,
    QGraphicsLineItem, QGraphicsRectItem
)
from PyQt6.QtCore import Qt, QRectF, pyqtSignal, QPointF
from PyQt6.QtGui import QPixmap, QWheelEvent, QMouseEvent, QPainter, QKeyEvent, QPen, QColor, QBrush

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class MinimapGraphicsView(QGraphicsView):
    """Custom QGraphicsView for minimap display with zoom, pan, and crosshair capabilities."""

    viewTransformed = pyqtSignal(float, QPointF)

    def __init__(self, parent=None):
        super().__init__(parent)

        self.setDragMode(QGraphicsView.DragMode.NoDrag)
        self.setRenderHint(QPainter.RenderHint.Antialiasing)
        self.setRenderHint(QPainter.RenderHint.SmoothPixmapTransform)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        self.zoom_factor = 1.0
        self.min_zoom = 0.1
        self.max_zoom = 20.0
        self.zoom_step = 1.15

        self.pan_enabled = True
        self.last_pan_point = QPointF()
        self.is_panning = False

        self.floor_camera_positions = {}
        self.current_floor_id = None

        self.crosshair_diagonals: List[Optional[QGraphicsLineItem]] = [None] * 8
        self.crosshair_center_square: Optional[QGraphicsRectItem] = None
        self.crosshair_inner_range: Optional[QGraphicsRectItem] = None
        self.crosshair_outer_range: Optional[QGraphicsRectItem] = None
        self.global_crosshair_position: Optional[QPointF] = None
        
    def wheelEvent(self, event: QWheelEvent):
        """Handle mouse wheel events for zooming."""
        zoom_in = event.angleDelta().y() > 0
        self.zoom(zoom_in, event.position())

    def mousePressEvent(self, event: QMouseEvent):
        """Handle mouse press events for panning and crosshair placement."""
        if event.button() == Qt.MouseButton.LeftButton:
            self.is_panning = True
            self.last_pan_point = event.position()
            self.setCursor(Qt.CursorShape.ClosedHandCursor)
        elif event.button() == Qt.MouseButton.RightButton:
            mouse_pos = event.position()
            mouse_point = QPointF(mouse_pos.x(), mouse_pos.y())
            scene_pos = self.mapToScene(mouse_point.toPoint())

            snapped_pos = QPointF(
                math.floor(scene_pos.x()) + 0.5,
                math.floor(scene_pos.y()) + 0.5
            )
            self.place_crosshairs(snapped_pos)
        else:
            super().mousePressEvent(event)
    
    def mouseMoveEvent(self, event: QMouseEvent):
        """Handle mouse move events for panning."""
        if self.is_panning:
            delta = event.position() - self.last_pan_point
            self.last_pan_point = event.position()

            self.horizontalScrollBar().setValue(
                self.horizontalScrollBar().value() - int(delta.x())
            )
            self.verticalScrollBar().setValue(
                self.verticalScrollBar().value() - int(delta.y())
            )
        else:
            super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event: QMouseEvent):
        """Handle mouse release events."""
        if event.button() == Qt.MouseButton.LeftButton:
            self.is_panning = False
            self.setCursor(Qt.CursorShape.ArrowCursor)
            self.emit_view_transformed()
        else:
            super().mouseReleaseEvent(event)

    def zoom(self, zoom_in: bool, center_point: Optional[QPointF] = None):
        """Zoom the view in or out."""
        factor = self.zoom_step if zoom_in else 1.0 / self.zoom_step
        new_zoom = self.zoom_factor * factor

        if new_zoom < self.min_zoom or new_zoom > self.max_zoom:
            return

        self.zoom_factor = new_zoom

        if center_point:
            self.setTransformationAnchor(QGraphicsView.ViewportAnchor.NoAnchor)
            center_pointf = QPointF(center_point.x(), center_point.y())
            old_pos = self.mapToScene(center_pointf.toPoint())
            self.scale(factor, factor)
            new_pos = self.mapToScene(center_pointf.toPoint())
            delta = new_pos - old_pos
            self.translate(delta.x(), delta.y())
        else:
            self.setTransformationAnchor(QGraphicsView.ViewportAnchor.AnchorViewCenter)
            self.scale(factor, factor)

        self.emit_view_transformed()
        self.update_crosshair_appearance()

    def zoom_to_factor(self, target_zoom: float):
        """Set zoom to a specific factor using absolute transformation."""
        if target_zoom < self.min_zoom or target_zoom > self.max_zoom:
            return

        self.resetTransform()
        self.scale(target_zoom, target_zoom)
        self.zoom_factor = target_zoom
        self.emit_view_transformed()
        self.update_crosshair_appearance()

    def fit_in_view_with_margin(self, margin_percent: float = 0.1):
        """Fit the scene in view with a margin."""
        if self.scene():
            scene_rect = self.scene().itemsBoundingRect()
            if not scene_rect.isEmpty():
                margin_x = scene_rect.width() * margin_percent
                margin_y = scene_rect.height() * margin_percent
                scene_rect.adjust(-margin_x, -margin_y, margin_x, margin_y)

                self.fitInView(scene_rect, Qt.AspectRatioMode.KeepAspectRatio)

                transform = self.transform()
                self.zoom_factor = transform.m11()
                self.emit_view_transformed()
                self.update_crosshair_appearance()

    def save_camera_position(self, floor_id=None):
        """Save current camera position (zoom is global)."""
        if floor_id is None:
            floor_id = self.current_floor_id

        if floor_id is None:
            logger.warning("Cannot save camera position - no floor ID specified")
            return

        viewport_center = self.viewport().rect().center()
        scene_center = self.mapToScene(viewport_center)

        if not scene_center.isNull() and self.scene():
            self.floor_camera_positions[floor_id] = {
                'center': QPointF(scene_center)
            }
        else:
            logger.warning("Could not save camera position - invalid scene coordinates")
    
    def restore_camera_position(self, floor_id=None):
        """Restore previously saved camera position (keep current global zoom)."""
        if floor_id is None:
            floor_id = self.current_floor_id

        if floor_id is None or floor_id not in self.floor_camera_positions:
            logger.info(f"No saved camera position for floor {floor_id}")
            return

        saved_data = self.floor_camera_positions[floor_id]
        saved_center = QPointF(saved_data['center'])

        scene_rect_before = self.scene().sceneRect() if self.scene() else QRectF()
        center_before = self.mapToScene(self.viewport().rect().center())

        viewport_rect = self.viewport().rect()
        target_viewport_center = self.mapFromScene(saved_center)

        current_viewport_center = viewport_rect.center()
        offset_x = target_viewport_center.x() - current_viewport_center.x()
        offset_y = target_viewport_center.y() - current_viewport_center.y()

        h_scroll = self.horizontalScrollBar()
        v_scroll = self.verticalScrollBar()
        h_scroll.setValue(h_scroll.value() + int(offset_x))
        v_scroll.setValue(v_scroll.value() + int(offset_y))

        center_after = self.mapToScene(self.viewport().rect().center())
        logger.info(f"RESTORE Floor {floor_id} - Target: ({saved_center.x():.2f}, {saved_center.y():.2f}), "
                   f"Before: ({center_before.x():.2f}, {center_before.y():.2f}), "
                   f"After: ({center_after.x():.2f}, {center_after.y():.2f}), "
                   f"Scene: {scene_rect_before.width():.0f}x{scene_rect_before.height():.0f}, "
                   f"Zoom: {self.zoom_factor:.4f} (GLOBAL - unchanged)")

        self.emit_view_transformed()
    
    def emit_view_transformed(self):
        """Emit signal when view is transformed."""
        center = self.mapToScene(self.viewport().rect().center())
        self.viewTransformed.emit(self.zoom_factor, center)

    def calculate_crosshair_pen_width(self, base_width: float) -> float:
        """Calculate appropriate pen width based on current zoom level."""
        min_visible_width = 1.0

        if self.zoom_factor >= 1.0:
            adjusted_base = max(base_width * 2.0, min_visible_width)
            return adjusted_base
        else:
            scale_factor = max(1.0 / self.zoom_factor, 1.0)
            scale_factor = min(scale_factor, 8.0)
            scaled_width = base_width * scale_factor
            return max(scaled_width, min_visible_width)

    def get_exiva_diagonal_angles(self) -> List[float]:
        """Get the 8 diagonal boundary angles used by Tibia's Exiva spell."""
        primary_angle_rad = math.atan(1.0 / 2.42)
        primary_angle_deg = math.degrees(primary_angle_rad)

        base_angles = [
            primary_angle_deg,                    # north-northeast (NNE)
            90.0 - primary_angle_deg,            # east-northeast (ENE)
            90.0 + primary_angle_deg,            # east-southeast (ESE)
            180.0 - primary_angle_deg,           # south-southeast (SSE)
            180.0 + primary_angle_deg,           # south-southwest (SSW)
            270.0 - primary_angle_deg,           # west-southwest (WSW)
            270.0 + primary_angle_deg,           # west-northwest (WNW)
            360.0 - primary_angle_deg            # north-northwest (NNW)
        ]

        return base_angles

    def get_crosshair_color(self) -> QColor:
        """Get the appropriate crosshair color based on current floor."""
        if self.current_floor_id == 7:
            return QColor(0, 0, 0, 255)  # Black for floor 07
        else:
            return QColor(255, 255, 255, 255)  # White for all other floors

    def calculate_boundary_line_endpoints(self, center_pos: QPointF, angle_deg: float) -> tuple[QPointF, QPointF]:
        """Calculate the endpoints of a diagonal line that extends to the scene boundaries."""
        if not self.scene():
            angle_rad = math.radians(angle_deg)
            half_length = 100.0
            start_x = center_pos.x() - half_length * math.sin(angle_rad)
            start_y = center_pos.y() + half_length * math.cos(angle_rad)
            end_x = center_pos.x() + half_length * math.sin(angle_rad)
            end_y = center_pos.y() - half_length * math.cos(angle_rad)
            return QPointF(start_x, start_y), QPointF(end_x, end_y)

        scene_rect = self.scene().sceneRect()
        angle_rad = math.radians(angle_deg)
        dx = math.sin(angle_rad)
        dy = -math.cos(angle_rad)

        intersections = []

        if dx != 0:
            t = (scene_rect.left() - center_pos.x()) / dx
            y = center_pos.y() + t * dy
            if scene_rect.top() <= y <= scene_rect.bottom():
                intersections.append(QPointF(scene_rect.left(), y))

        if dx != 0:
            t = (scene_rect.right() - center_pos.x()) / dx
            y = center_pos.y() + t * dy
            if scene_rect.top() <= y <= scene_rect.bottom():
                intersections.append(QPointF(scene_rect.right(), y))

        if dy != 0:
            t = (scene_rect.top() - center_pos.y()) / dy
            x = center_pos.x() + t * dx
            if scene_rect.left() <= x <= scene_rect.right():
                intersections.append(QPointF(x, scene_rect.top()))

        if dy != 0:
            t = (scene_rect.bottom() - center_pos.y()) / dy
            x = center_pos.x() + t * dx
            if scene_rect.left() <= x <= scene_rect.right():
                intersections.append(QPointF(x, scene_rect.bottom()))

        unique_intersections = []
        for point in intersections:
            is_duplicate = False
            for existing in unique_intersections:
                if abs(point.x() - existing.x()) < 0.1 and abs(point.y() - existing.y()) < 0.1:
                    is_duplicate = True
                    break
            if not is_duplicate:
                unique_intersections.append(point)

        if len(unique_intersections) >= 2:
            def get_t_value(point):
                if abs(dx) > abs(dy):
                    return (point.x() - center_pos.x()) / dx if dx != 0 else 0
                else:
                    return (point.y() - center_pos.y()) / dy if dy != 0 else 0

            unique_intersections.sort(key=get_t_value)
            return unique_intersections[0], unique_intersections[-1]

        max_dimension = max(scene_rect.width(), scene_rect.height())
        half_length = max_dimension
        start_x = center_pos.x() - half_length * dx
        start_y = center_pos.y() - half_length * dy
        end_x = center_pos.x() + half_length * dx
        end_y = center_pos.y() + half_length * dy

        return QPointF(start_x, start_y), QPointF(end_x, end_y)

    def calculate_diagonal_line_length(self) -> float:
        """Calculate the length of diagonal lines based on current zoom level."""
        base_length = 100.0

        if self.zoom_factor >= 1.0:
            return base_length * min(2.0, self.zoom_factor)
        else:
            scale_factor = max(1.0, 1.0 / self.zoom_factor)
            scale_factor = min(scale_factor, 10.0)
            return base_length * scale_factor

    def calculate_exiva_range_size(self, range_squares: int) -> float:
        """Calculate the size of an Exiva range rectangle in scene coordinates."""
        return float((range_squares * 2) + 1)

    def place_crosshairs(self, scene_pos: QPointF):
        """Place Exiva-style crosshairs with 8-directional lines and distance ranges."""
        if not self.scene():
            return

        self.remove_crosshairs()
        self.global_crosshair_position = QPointF(scene_pos)

        crosshair_width = self.calculate_crosshair_pen_width(0.3)
        angles = self.get_exiva_diagonal_angles()

        crosshair_color = self.get_crosshair_color()
        crosshair_pen = QPen(crosshair_color)
        crosshair_pen.setWidthF(crosshair_width)
        crosshair_pen.setStyle(Qt.PenStyle.SolidLine)
        crosshair_pen.setCosmetic(True)

        for i, angle_deg in enumerate(angles):
            start_point, end_point = self.calculate_boundary_line_endpoints(scene_pos, angle_deg)

            diagonal_line = QGraphicsLineItem(start_point.x(), start_point.y(), end_point.x(), end_point.y())
            diagonal_line.setPen(crosshair_pen)
            diagonal_line.setZValue(999)
            self.scene().addItem(diagonal_line)
            self.crosshair_diagonals[i] = diagonal_line

        self.create_exiva_ranges(scene_pos, crosshair_width)

        base_square_size = 1.0
        square_size = base_square_size * max(1.0, min(2.0, 1.0 / self.zoom_factor)) if self.zoom_factor < 1.0 else base_square_size

        self.crosshair_center_square = QGraphicsRectItem(
            scene_pos.x() - square_size/2, scene_pos.y() - square_size/2,
            square_size, square_size
        )
        self.crosshair_center_square.setPen(crosshair_pen)
        self.crosshair_center_square.setBrush(QBrush(Qt.BrushStyle.NoBrush))
        self.crosshair_center_square.setZValue(1001)
        self.scene().addItem(self.crosshair_center_square)

        logger.info(f"Exiva crosshairs placed at ({scene_pos.x():.2f}, {scene_pos.y():.2f}) on floor {self.current_floor_id} with 8 diagonal boundary lines and distance ranges")

    def create_exiva_ranges(self, center_pos: QPointF, range_width: float):
        """Create Exiva spell distance range overlays."""
        crosshair_color = self.get_crosshair_color()
        range_pen = QPen(crosshair_color)
        range_pen.setWidthF(range_width)
        range_pen.setStyle(Qt.PenStyle.SolidLine)
        range_pen.setCosmetic(True)

        inner_size = self.calculate_exiva_range_size(100)

        self.crosshair_inner_range = QGraphicsRectItem(
            center_pos.x() - inner_size/2, center_pos.y() - inner_size/2,
            inner_size, inner_size
        )
        self.crosshair_inner_range.setPen(range_pen)
        self.crosshair_inner_range.setBrush(QBrush(Qt.BrushStyle.NoBrush))
        self.crosshair_inner_range.setZValue(998)
        self.scene().addItem(self.crosshair_inner_range)

        outer_size = self.calculate_exiva_range_size(250)

        self.crosshair_outer_range = QGraphicsRectItem(
            center_pos.x() - outer_size/2, center_pos.y() - outer_size/2,
            outer_size, outer_size
        )
        self.crosshair_outer_range.setPen(range_pen)
        self.crosshair_outer_range.setBrush(QBrush(Qt.BrushStyle.NoBrush))
        self.crosshair_outer_range.setZValue(997)
        self.scene().addItem(self.crosshair_outer_range)

    def update_crosshair_appearance(self):
        """Update the appearance of existing Exiva crosshairs based on current zoom level."""
        if not self.scene() or self.global_crosshair_position is None:
            return

        has_diagonals = any(line is not None for line in self.crosshair_diagonals)
        if not (has_diagonals or self.crosshair_center_square):
            return

        crosshair_width = self.calculate_crosshair_pen_width(0.3)

        crosshair_color = self.get_crosshair_color()
        crosshair_pen = QPen(crosshair_color)
        crosshair_pen.setWidthF(crosshair_width)
        crosshair_pen.setStyle(Qt.PenStyle.SolidLine)
        crosshair_pen.setCosmetic(True)

        angles = self.get_exiva_diagonal_angles()
        scene_pos = self.global_crosshair_position

        for diagonal_line, angle_deg in zip(self.crosshair_diagonals, angles):
            if diagonal_line is not None:
                diagonal_line.setPen(crosshair_pen)
                start_point, end_point = self.calculate_boundary_line_endpoints(scene_pos, angle_deg)
                diagonal_line.setLine(start_point.x(), start_point.y(), end_point.x(), end_point.y())

        if self.crosshair_inner_range:
            self.crosshair_inner_range.setPen(crosshair_pen)

        if self.crosshair_outer_range:
            self.crosshair_outer_range.setPen(crosshair_pen)

        if self.crosshair_center_square:
            self.crosshair_center_square.setPen(crosshair_pen)

            base_square_size = 1.0
            square_size = base_square_size * max(1.0, min(2.0, 1.0 / self.zoom_factor)) if self.zoom_factor < 1.0 else base_square_size

            self.crosshair_center_square.setRect(
                scene_pos.x() - square_size/2, scene_pos.y() - square_size/2,
                square_size, square_size
            )

        logger.debug(f"Updated Exiva crosshair appearance: crosshair width {crosshair_width:.3f}, lines extend to scene boundaries")

    def remove_crosshairs(self):
        """Remove existing Exiva crosshairs from the scene."""
        for i in range(8):
            if self.crosshair_diagonals[i] and self.scene():
                try:
                    self.scene().removeItem(self.crosshair_diagonals[i])
                except RuntimeError:
                    pass
                self.crosshair_diagonals[i] = None

        if self.crosshair_center_square and self.scene():
            try:
                self.scene().removeItem(self.crosshair_center_square)
            except RuntimeError:
                pass
            self.crosshair_center_square = None

        if self.crosshair_inner_range and self.scene():
            try:
                self.scene().removeItem(self.crosshair_inner_range)
            except RuntimeError:
                pass
            self.crosshair_inner_range = None

        if self.crosshair_outer_range and self.scene():
            try:
                self.scene().removeItem(self.crosshair_outer_range)
            except RuntimeError:
                pass
            self.crosshair_outer_range = None

        self.global_crosshair_position = None

    def restore_crosshairs(self, _floor_id: int = None):
        """Restore global crosshairs if they exist."""
        if not self.scene():
            logger.warning("Cannot restore crosshairs - no scene available")
            return

        if self.global_crosshair_position is not None:
            self.place_crosshairs(self.global_crosshair_position)

    def save_crosshair_position(self, _floor_id: int = None):
        """Save current global crosshair position (no-op since crosshairs are already global)."""
        pass

    def clear_all_crosshairs(self):
        """Clear global crosshairs."""
        self.remove_crosshairs()


class MinimapViewer(QWidget):
    """Main minimap viewer widget with floor navigation and zoom controls."""

    floorChanged = pyqtSignal(int)

    def __init__(self, minimap_dir: str = "processed_minimap", parent=None):
        super().__init__(parent)

        self.minimap_dir = Path(minimap_dir)
        self.current_floor = 7
        self.floor_images: Dict[int, QPixmap] = {}

        self.floor_order = [
            0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15
        ]

        self.setup_ui()
        self.load_floor_images()
        self.set_floor(self.current_floor)
    
    def setup_ui(self):
        """Set up the user interface with vertical layout and sidebars."""
        # Main horizontal layout
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)

        # Left sidebar for controls
        left_sidebar = QFrame()
        left_sidebar.setFrameStyle(QFrame.Shape.StyledPanel)
        left_sidebar.setFixedWidth(200)
        left_sidebar.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Expanding)

        left_layout = QVBoxLayout(left_sidebar)
        left_layout.setContentsMargins(10, 10, 10, 10)
        left_layout.setSpacing(10)

        # Floor controls section
        floor_group = QFrame()
        floor_group.setFrameStyle(QFrame.Shape.Box)
        floor_group_layout = QVBoxLayout(floor_group)
        floor_group_layout.setContentsMargins(8, 8, 8, 8)
        floor_group_layout.setSpacing(5)

        floor_title = QLabel("Floor Navigation")
        floor_title.setStyleSheet("font-weight: bold; font-size: 12px;")
        floor_group_layout.addWidget(floor_title)

        floor_label = QLabel("Floor:")
        floor_group_layout.addWidget(floor_label)

        # Floor navigation buttons and combo
        floor_nav_layout = QHBoxLayout()
        floor_nav_layout.setSpacing(8)  # Add proper spacing between elements

        self.floor_down_btn = QPushButton("-")
        self.floor_down_btn.setMaximumWidth(30)
        self.floor_down_btn.setToolTip("Go down one floor")
        self.floor_down_btn.clicked.connect(self.floor_down)

        self.floor_combo = QComboBox()
        self.floor_combo.setMinimumWidth(100)
        self.floor_combo.currentTextChanged.connect(self.on_floor_changed)

        self.floor_up_btn = QPushButton("+")
        self.floor_up_btn.setMaximumWidth(30)
        self.floor_up_btn.setToolTip("Go up one floor")
        self.floor_up_btn.clicked.connect(self.floor_up)

        floor_nav_layout.addWidget(self.floor_down_btn)
        floor_nav_layout.addWidget(self.floor_combo)
        floor_nav_layout.addWidget(self.floor_up_btn)
        floor_group_layout.addLayout(floor_nav_layout)

        # Add groups to left sidebar
        left_layout.addWidget(floor_group)
        left_layout.addStretch()  # Push content to top

        # Center area for main content
        center_frame = QFrame()
        center_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        center_layout = QVBoxLayout(center_frame)
        center_layout.setContentsMargins(0, 0, 0, 0)

        self.graphics_view = MinimapGraphicsView()
        self.graphics_scene = QGraphicsScene()
        self.graphics_view.setScene(self.graphics_scene)

        center_layout.addWidget(self.graphics_view)

        # Right sidebar (empty for now)
        right_sidebar = QFrame()
        right_sidebar.setFrameStyle(QFrame.Shape.StyledPanel)
        right_sidebar.setFixedWidth(200)
        right_sidebar.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Expanding)

        right_layout = QVBoxLayout(right_sidebar)
        right_layout.setContentsMargins(10, 10, 10, 10)
        right_layout.setSpacing(10)

        # Placeholder content for right sidebar
        right_title = QLabel("Additional Tools")
        right_title.setStyleSheet("font-weight: bold; font-size: 12px;")
        right_layout.addWidget(right_title)

        placeholder_label = QLabel("(Reserved for future features)")
        placeholder_label.setStyleSheet("color: gray; font-style: italic;")
        right_layout.addWidget(placeholder_label)
        right_layout.addStretch()

        # Add all sections to main layout
        main_layout.addWidget(left_sidebar)
        main_layout.addWidget(center_frame, 1)  # Center gets all extra space
        main_layout.addWidget(right_sidebar)

        self.current_minimap_item: Optional[QGraphicsPixmapItem] = None

        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.setFocusPolicy(Qt.FocusPolicy.StrongFocus)

    def load_floor_images(self):
        """Load all available floor images."""
        logger.info(f"Loading floor images from {self.minimap_dir}")

        if not self.minimap_dir.exists():
            logger.error(f"Minimap directory not found: {self.minimap_dir}")
            return

        loaded_floors = []
        for floor in self.floor_order:
            floor_file = self.minimap_dir / f"floor_{floor:02d}.png"
            if floor_file.exists():
                try:
                    pixmap = QPixmap(str(floor_file))
                    if not pixmap.isNull():
                        self.floor_images[floor] = pixmap
                        loaded_floors.append(floor)
                    else:
                        logger.warning(f"Failed to load floor image: {floor_file}")
                except Exception as e:
                    logger.error(f"Error loading floor {floor}: {e}")

        self.floor_combo.blockSignals(True)
        self.floor_combo.clear()
        for floor in self.floor_order:
            if floor in self.floor_images:
                if floor == 7:
                    self.floor_combo.addItem(f"Floor {floor:02d} (Main)", floor)
                else:
                    self.floor_combo.addItem(f"Floor {floor:02d}", floor)
        self.floor_combo.blockSignals(False)

        logger.info(f"Loaded {len(loaded_floors)} floor images: {loaded_floors}")

    def set_floor(self, floor: int):
        """Set the current floor and display its minimap."""
        if floor not in self.floor_images:
            logger.warning(f"Floor {floor} not available")
            return

        self.current_floor = floor
        self.graphics_view.current_floor_id = floor

        self.graphics_scene.clear()

        self.graphics_view.crosshair_diagonals = [None] * 8
        self.graphics_view.crosshair_center_square = None
        self.graphics_view.crosshair_inner_range = None
        self.graphics_view.crosshair_outer_range = None

        current_zoom = self.graphics_view.zoom_factor
        current_center = self.graphics_view.mapToScene(self.graphics_view.viewport().rect().center())

        pixmap = self.floor_images[floor]
        self.current_minimap_item = QGraphicsPixmapItem(pixmap)
        self.graphics_scene.addItem(self.current_minimap_item)

        new_scene_rect = self.current_minimap_item.boundingRect()
        self.graphics_scene.setSceneRect(new_scene_rect)

        logger.info(f"FLOOR_CHANGE - Floor {floor}: Scene rect {new_scene_rect.width():.0f}x{new_scene_rect.height():.0f}")

        if hasattr(self, '_first_floor_loaded'):
            self.graphics_view.zoom_to_factor(current_zoom)

            viewport_rect = self.graphics_view.viewport().rect()
            target_viewport_center = self.graphics_view.mapFromScene(current_center)
            current_viewport_center = viewport_rect.center()
            offset_x = target_viewport_center.x() - current_viewport_center.x()
            offset_y = target_viewport_center.y() - current_viewport_center.y()

            h_scroll = self.graphics_view.horizontalScrollBar()
            v_scroll = self.graphics_view.verticalScrollBar()
            h_scroll.setValue(h_scroll.value() + int(offset_x))
            v_scroll.setValue(v_scroll.value() + int(offset_y))

            logger.info(f"GLOBAL CAMERA - Floor {floor}: Zoom: {current_zoom:.4f}, Position: ({current_center.x():.2f}, {current_center.y():.2f}) - UNCHANGED")
        else:
            self.fit_view()
            self._first_floor_loaded = True

        self.graphics_view.restore_crosshairs(floor)

        self.floor_combo.blockSignals(True)
        for i in range(self.floor_combo.count()):
            if self.floor_combo.itemData(i) == floor:
                self.floor_combo.setCurrentIndex(i)
                break
        self.floor_combo.blockSignals(False)

        logger.info(f"Switched to floor {floor}")
        self.floorChanged.emit(floor)

    def on_floor_changed(self, _floor_text: str):
        """Handle floor selection change from combo box."""
        current_data = self.floor_combo.currentData()
        if current_data is not None:
            self.set_floor(current_data)

    def floor_up(self):
        """Navigate to the next floor up (lower floor number)."""
        current_index = self.floor_order.index(self.current_floor)
        if current_index > 0:
            next_floor = self.floor_order[current_index - 1]
            if next_floor in self.floor_images:
                self.set_floor(next_floor)

    def floor_down(self):
        """Navigate to the next floor down (higher floor number)."""
        current_index = self.floor_order.index(self.current_floor)
        if current_index < len(self.floor_order) - 1:
            next_floor = self.floor_order[current_index + 1]
            if next_floor in self.floor_images:
                self.set_floor(next_floor)

    def keyPressEvent(self, event: QKeyEvent):
        """Handle keyboard shortcuts."""
        if event.key() == Qt.Key.Key_Plus or event.key() == Qt.Key.Key_Equal:
            self.floor_up()
        elif event.key() == Qt.Key.Key_Minus:
            self.floor_down()
        elif event.key() == Qt.Key.Key_C and event.modifiers() == Qt.KeyboardModifier.ControlModifier:
            self.clear_crosshairs()
        else:
            super().keyPressEvent(event)

    def clear_crosshairs(self):
        """Clear global crosshairs."""
        self.graphics_view.clear_all_crosshairs()
        logger.info("Cleared global crosshairs")

    def fit_view(self):
        """Fit the minimap in the view."""
        self.graphics_view.fit_in_view_with_margin(0.05)

    def get_current_floor(self) -> int:
        """Get the currently displayed floor."""
        return self.current_floor

    def get_available_floors(self) -> list[int]:
        """Get list of available floors."""
        return list(self.floor_images.keys())

    def get_camera_info(self) -> dict:
        """Get current camera information."""
        center = self.graphics_view.mapToScene(self.graphics_view.viewport().rect().center())
        return {
            'floor': self.current_floor,
            'zoom_factor': self.graphics_view.zoom_factor,
            'center_x': center.x(),
            'center_y': center.y()
        }


class MinimapViewerWindow(QMainWindow):
    """Main window for the minimap viewer application."""

    def __init__(self, minimap_dir: str = "processed_minimap"):
        super().__init__()

        self.setWindowTitle("FiendishFinder - Minimap Viewer")
        self.setGeometry(100, 100, 1200, 800)

        self.minimap_viewer = MinimapViewer(minimap_dir)
        self.setCentralWidget(self.minimap_viewer)

        self.minimap_viewer.floorChanged.connect(self.on_floor_changed)

        self.status_bar = self.statusBar()
        self.update_status()

    def on_floor_changed(self, _floor: int):
        """Handle floor change events."""
        self.update_status()

    def update_status(self):
        """Update status bar with current information."""
        camera_info = self.minimap_viewer.get_camera_info()
        available_floors = len(self.minimap_viewer.get_available_floors())

        status_text = (
            f"Floor: {camera_info['floor']:02d} | "
            f"Zoom: {camera_info['zoom_factor']:.1f}x | "
            f"Position: ({camera_info['center_x']:.0f}, {camera_info['center_y']:.0f}) | "
            f"Available Floors: {available_floors}"
        )

        self.status_bar.showMessage(status_text)


def main():
    """Main function to run the minimap viewer application."""
    app = QApplication(sys.argv)

    app.setApplicationName("FiendishFinder Minimap Viewer")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("FiendishFinder")

    window = MinimapViewerWindow()

    minimap_dir = Path("processed_minimap")
    if not minimap_dir.exists():
        QMessageBox.warning(
            window,
            "Directory Not Found",
            f"Minimap directory '{minimap_dir}' not found.\n"
            "Please ensure the processed minimap images are available."
        )

    window.show()
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
